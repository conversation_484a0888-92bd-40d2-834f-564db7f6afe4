#!/bin/bash

XPROFILE="$HOME/.xprofile"
CMD="setxkbmap -option '' -option ctrl:swap_lalt_lctl"

if grep -Fxq "$CMD" "$XPROFILE" 2>/dev/null; then
    echo "✔️ Comando já está presente em ~/.xprofile"
else
    echo "🔧 Adicionando comando ao ~/.xprofile..."
    echo "" >> "$XPROFILE"
    echo "# Inverte Ctrl e Alt (esquerda)" >> "$XPROFILE"
    echo "$CMD" >> "$XPROFILE"
fi

chmod +x "$XPROFILE"

echo "✅ Configuração aplicada. Reinicie a sessão para testar."

